"""
pytest 配置文件
"""
import os
import pytest
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, BrowserContext


def pytest_addoption(parser):
    """添加自定义命令行参数"""
    parser.addoption(
        "--playwright-headless",
        action="store_true",
        default=False,
        help="运行 Playwright 测试时使用无头模式"
    )


@pytest.fixture(scope="session")
def browser_context_args(browser_context_args):
    """浏览器上下文配置"""
    return {
        **browser_context_args,
        "viewport": {"width": 1920, "height": 1080},
        "ignore_https_errors": True,
    }


@pytest.fixture(scope="session")
def browser_type_launch_args(browser_type_launch_args, request):
    """浏览器启动参数配置 - 使用 Chrome，支持动态 headless 控制"""

    # 优先级：命令行参数 > 环境变量 > 默认值
    headless = False

    # 1. 检查命令行参数
    if hasattr(request.config, 'getoption'):
        headless = request.config.getoption("--playwright-headless")

    # 2. 检查环境变量（如果命令行参数未设置）
    if not headless:
        env_headless = os.getenv("PLAYWRIGHT_HEADLESS", "").lower()
        headless = env_headless in ("true", "1", "yes", "on")

    return {
        **browser_type_launch_args,
        "channel": "chrome",  # 使用系统安装的 Chrome
        "headless": headless, # 动态控制无头模式
        "slow_mo": 100,       # 操作间隔100ms，便于观察
    }


@pytest.fixture
def page_setup(page: Page):
    """页面设置"""
    # 设置默认超时时间
    page.set_default_timeout(30000)
    page.set_default_navigation_timeout(30000)
    
    yield page
    
    # 测试结束后清理
    try:
        page.close()
    except:
        pass


@pytest.fixture
def api_context(playwright):
    """API 测试上下文"""
    context = playwright.request.new_context(
        base_url="https://httpbin.org",
        extra_http_headers={
            "User-Agent": "Playwright Test Agent"
        }
    )
    yield context
    context.dispose()
