# Playwright pytest --headless 参数修复任务

## 问题描述
用户在使用 Web 控制台运行测试时，遇到 `--headless` 参数错误：
```
pytest: error: unrecognized arguments: --headless
```

## 根本原因
`test_dashboard.py` 文件第78行错误地将 `--headless` 参数直接传递给 pytest 命令，但 pytest 不认识这个参数。正确的做法是通过 Playwright 的配置来控制无头模式。

## 解决方案
采用方案1：修改 conftest.py 配置，通过环境变量或命令行参数控制 Playwright 的 headless 模式。

## 执行计划
1. 修改 conftest.py 配置支持动态 headless 控制
2. 修复 test_dashboard.py 的错误参数传递
3. 添加自定义 pytest 参数支持
4. 更新 pytest.ini 配置
5. 测试验证

## 开始时间
2025-07-30

## 状态
执行中
